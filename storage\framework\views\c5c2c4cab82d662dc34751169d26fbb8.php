<?php ($galleries = get_galleries(isset($shortcode) && (int) $shortcode->limit ? (int) $shortcode->limit : ($limit ?: 6))); ?>

<?php if(! $galleries->isEmpty()): ?>
    <section class="shop-area pb-100">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="tpsectionarea text-center mb-35">
                        <?php if($subtitle = $shortcode->subtitle): ?>
                            <h5 class="tpsectionarea__subtitle"><?php echo BaseHelper::clean($subtitle); ?></h5>
                        <?php endif; ?>
                        <?php if($title = $shortcode->title): ?>
                            <h4 class="tpsectionarea__title">
                                <?php echo BaseHelper::clean($title); ?>

                            </h4>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="shopareaitem">
                <div class="shopslider-active swiper-container">
                    <div class="swiper-wrapper">
                        <?php $__currentLoopData = $galleries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gallery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="tpshopitem swiper-slide">
                                <?php if($image = RvMedia::getImageUrl($gallery->image, 'medium')): ?>
                                    <a href="<?php echo e($gallery->url); ?>">
                                        <img src="<?php echo e($image); ?>" alt="<?php echo e($gallery->name); ?>">
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/shortcodes/galleries/index.blade.php ENDPATH**/ ?>