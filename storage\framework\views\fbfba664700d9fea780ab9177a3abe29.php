<div class="container">
    <div class="row justify-content-xl-end">
        <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['col-xl-9 col-xxl-7 col-lg-9' => $hasAds, 'col-xl-12 col-xxl-10 col-lg-12' => ! $hasAds]); ?>">
            <div class="tp-slider-area p-relative">
                <div class="swiper-container slider-active">
                    <div class="swiper-wrapper">
                        <?php $__currentLoopData = $sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="swiper-slide">
                                <div class="tp-slide-item">
                                    <div class="tp-slide-item__content">
                                        <?php if($slider->description): ?>
                                            <h4 class="tp-slide-item__sub-title"><?php echo BaseHelper::clean($slider->description); ?></h4>
                                        <?php endif; ?>
                                        <?php if($slider->title): ?>
                                            <h3 class="tp-slide-item__title mb-25"><?php echo BaseHelper::clean($slider->title); ?></h3>
                                        <?php endif; ?>
                                        <?php if(($actionLabel = $slider->getMetaData('action_label', true)) && $slider->link): ?>
                                            <a class="tp-slide-item__slide-btn tp-btn" href="<?php echo e($slider->link); ?>">
                                                <?php echo e($actionLabel); ?> <i class="fal fa-long-arrow-right"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                    <div class="tp-slide-item__img">
                                        <?php echo $__env->make(Theme::getThemeNamespace('partials.shortcodes.simple-slider.includes.image', compact('slider')), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <div class="slider-pagination"></div>
            </div>
        </div>
        <?php if(is_plugin_active('ads') && $hasAds): ?>
            <div class="col-xl-3 col-xxl-3 col-lg-3">
                <div class="row">
                    <?php $__currentLoopData = range(1, 2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($ads = AdsManager::getAds($shortcode->{'ads_' . $i})): ?>
                            <div class="col-lg-12 col-md-6">
                                <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['tpslider-banner', 'tp-slider-sm-banner mb-30' => $loop->first]); ?>">
                                    <a href="<?php echo e($ads->url); ?>">
                                        <div class="tpslider-banner__img">
                                            <img src="<?php echo e(RvMedia::getImageUrl($ads->image, null, false, RvMedia::getDefaultImage())); ?>" alt="<?php echo e($ads->name); ?>">
                                            <?php if($shortcode->show_ads_title !== '0'): ?>
                                                <div class="tpslider-banner__content">
                                                    <h4 class="tpslider-banner__title"><?php echo BaseHelper::clean($ads->name); ?></h4>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/shortcodes/simple-slider/styles/wooden.blade.php ENDPATH**/ ?>