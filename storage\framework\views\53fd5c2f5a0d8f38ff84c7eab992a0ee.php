<?php
    $hasAds = $shortcode->ads_1 || $shortcode->ads_2;
    $style = ! in_array($shortcode->style, ['wooden', 'fashion', 'furniture', 'cosmetics', 'grocery', 'full-width']) ? 'wooden' : $shortcode->style;
?>

<?php if($sliders->isNotEmpty()): ?>
    <?php $sliders->loadMissing('metadata'); ?>
    <section class="<?php echo \Illuminate\Support\Arr::toCssClasses([
        'slider-area',
        'pb-25' => $style === 'wooden',
        'slider-bg slider-bg-height' => $style === 'fashion',
    ]); ?>" <?php if($shortcode->background_color): ?> style="background-color: <?php echo e($shortcode->background_color); ?> !important;" <?php endif; ?>>
        <?php echo $__env->make(Theme::getThemeNamespace("partials.shortcodes.simple-slider.styles.$style"), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </section>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/shortcodes/simple-slider/index.blade.php ENDPATH**/ ?>