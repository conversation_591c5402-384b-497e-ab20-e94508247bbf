<?php echo $__env->make('plugins/ecommerce::themes.includes.product-price', [
    'product' => $product,
    'priceClassName' => $priceClassName ?? 'product-price-sale',
    'priceOriginalClassName' => $priceOriginalClassName ?? 'product-price-original',
], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/views/ecommerce/includes/product-price.blade.php ENDPATH**/ ?>