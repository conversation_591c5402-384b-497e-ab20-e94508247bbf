<?php if (! $__env->hasRenderedOnce('4d9993e5-22ba-41fc-ae35-2057ee3d249e')): $__env->markAsRenderedOnce('4d9993e5-22ba-41fc-ae35-2057ee3d249e'); ?>
    <div
        class="offcanvas offcanvas-end"
        tabindex="-1"
        id="notification-sidebar"
        aria-labelledby="notification-sidebar-label"
        data-url="<?php echo e(route('notifications.index')); ?>"
        data-count-url="<?php echo e(route('notifications.count-unread')); ?>"
    >
        <button
            type="button"
            class="btn-close text-reset"
            data-bs-dismiss="offcanvas"
            aria-label="Close"
        ></button>

        <div class="notification-content"></div>
    </div>

    <script src="<?php echo e(asset('vendor/core/core/base/js/notification.js')); ?>"></script>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\main\platform/core/base/resources/views/notification/notification.blade.php ENDPATH**/ ?>