<?php if($products->isNotEmpty()): ?>
    <?php ($currentLayout = $currentLayout ?? get_current_product_layout()); ?>

    <?php echo $__env->make(Theme::getThemeNamespace("views.ecommerce.includes.product-$currentLayout"), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <?php if($products instanceof \Illuminate\Contracts\Pagination\LengthAwarePaginator): ?>
        <div class="row mt-50">
            <div class="col-xxl-12 text-center pb-50">
                <?php echo e($products->links(Theme::getThemeNamespace('partials.pagination'))); ?>

            </div>
        </div>
    <?php endif; ?>
<?php else: ?>
    <div class="my-5 text-center">
        <p class="text-muted"><?php echo e(__('No products found!')); ?></p>
    </div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/views/ecommerce/includes/product-items.blade.php ENDPATH**/ ?>