<div class="tpproduct pb-15 mb-30">
    <div class="tpproduct__thumb p-relative">
        <div class="product__badge-list">
            <?php if($product->isOutOfStock()): ?>
                <span class="tpproduct__thumb-topsall" style="background-color: #ff0000">
                    <span class="product__badge-item"><?php echo e(__('Out of stock')); ?></span>
                </span>
            <?php else: ?>
                <?php if($product->isOnSale() && $product->sale_percent): ?>
                    <span class="tpproduct__thumb-topsall" style="background-color: #328f0a">
                        <span class="product__badge-item"><?php echo e(__(':percent% off', ['percent' => $product->sale_percent])); ?></span>
                    </span>
                <?php endif; ?>
                <?php if($product->productLabels->isNotEmpty()): ?>
                    <?php $__currentLoopData = $product->productLabels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <span class="tpproduct__thumb-topsall" <?php echo $label->css_styles; ?>>
                            <span class="product__badge-item"><?php echo e($label->name); ?></span>
                        </span>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        <a href="<?php echo e($product->url); ?>">
            <img src="<?php echo e(RvMedia::getImageUrl($product->image, 'small', false, RvMedia::getDefaultImage())); ?>" alt="<?php echo e($product->name); ?>">
            <img class="product-thumb-secondary" src="<?php echo e(RvMedia::getImageUrl(Arr::get($product->images, 2, $product->image), 'small', false, RvMedia::getDefaultImage())); ?>" alt="<?php echo e($product->name); ?>">
        </a>
        <div class="tpproduct__thumb-action">
            <?php if(EcommerceHelper::isCompareEnabled()): ?>
                <a
                    class="add-to-compare"
                    href="#"
                    title="<?php echo e(__('Add to compare')); ?>"
                    data-url="<?php echo e(route('public.compare.add', $product->getKey())); ?>"
                ><i class="fal fa-exchange"></i>
                </a>
            <?php endif; ?>
            <a class="quickview" href="#" data-url="<?php echo e(route('public.ajax.quick-view', $product->id)); ?>"><i class="fal fa-eye"></i></a>
            <?php if(EcommerceHelper::isWishlistEnabled()): ?>
                <a class="wishlist add-to-wishlist" href="#"
                   title="<?php echo e(__('Add to wishlist')); ?>"
                   data-url="<?php echo e(route('public.wishlist.add', $product->getKey())); ?>"><i class="fal fa-heart"></i></a>
            <?php endif; ?>
        </div>
    </div>
    <div class="tpproduct__content">
        <h3 class="tpproduct__title text-truncate">
            <a href="<?php echo e($product->url); ?>" title="<?php echo e($product->name); ?>"><?php echo e($product->name); ?></a>
        </h3>

        <?php if(EcommerceHelper::isReviewEnabled()): ?>
            <div class="mb-2" style="margin-top: -0.75rem">
                <div class="product-rating-wrapper">
                    <div class="product-rating" style="width: <?php echo e($product->reviews_avg * 20); ?>%"></div>
                </div>
                <a class="tpproduct-details__reviewers" href="<?php echo e($product->url); ?>#reviews">(<?php echo e($product->reviews_count); ?>)</a>
            </div>
        <?php endif; ?>

        <div class="tpproduct__priceinfo p-relative">
            <div class="tpproduct__priceinfo-list">
                <?php echo $__env->make(EcommerceHelper::viewPath('includes.product-price'), [
                    'product' => $product,
                    'priceOriginalClassName' => 'tpproduct__priceinfo-list-oldprice',
                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
            <?php if(EcommerceHelper::isCartEnabled()): ?>
                <div class="tpproduct__cart">
                    <?php if($product->variations()->exists()): ?>
                        <a data-id="<?php echo e($product->slug); ?>" href="#" data-url="<?php echo e(route('public.ajax.quick-shop', $product->slug)); ?>" class="button-quick-shop">
                            <i class="fal fa-shopping-cart"></i>
                            <span><?php echo e(__('Select options')); ?></span>
                        </a>
                    <?php else: ?>
                        <a data-id="<?php echo e($product->id); ?>" href="#" data-url="<?php echo e(route('public.cart.add-to-cart')); ?>" class="add-to-cart">
                            <i class="fal fa-shopping-cart"></i>
                            <span><?php echo e(__('Add To Cart')); ?></span>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/views/ecommerce/includes/product-item.blade.php ENDPATH**/ ?>