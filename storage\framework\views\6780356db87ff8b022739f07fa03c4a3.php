<section class="dealproduct-area pt-30 pb-30">
    <div class="container">
        <div class="theme-bg pt-40 pb-40" <?php if($shortcode->background_color): ?> style="background-color: <?php echo e($shortcode->background_color); ?> !important;" <?php endif; ?>>
            <div class="row">
                <div class="col-lg-6 col-md-12">
                    <div class="tpdealproduct">
                        <div class="tpdealproduct__thumb p-relative text-center">
                            <img src="<?php echo e(RvMedia::getImageUrl($product->image, 'small', false, RvMedia::getDefaultImage())); ?>" alt="<?php echo e($product->name); ?>">
                            <div class="tpdealproductd__offer">
                                <p class="tpdealproduct__offer-price">
                                    <span><?php echo e(__('From')); ?></span><?php echo e(format_price($product->front_sale_price_with_taxes)); ?>

                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-12">
                    <div class="tpdealcontact pt-30">
                        <div class="tpdealcontact__price mb-5">
                            <?php echo $__env->make(EcommerceHelper::viewPath('includes.product-price'), ['product' => $product], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </div>
                        <div class="tpdealcontact__text mb-30">
                            <h4 class="tpdealcontact__title mb-10">
                                <a href="<?php echo e($product->url); ?>"><?php echo e($product->name); ?></a>
                            </h4>
                            <p><?php echo BaseHelper::clean($product->description); ?></p>
                        </div>
                        <div class="tpdealcontact__progress mb-30">
                            <div class="progress">
                                <div class="progress-bar" style="width: <?php echo e($product->pivot->quantity > 0 ? ($product->pivot->sold / $product->pivot->quantity) * 100 : 0); ?>%"></div>
                            </div>
                        </div>
                        <div class="tpdealcontact__count">
                            <div class="tpdealcontact__countdown" data-countdown="<?php echo e($flashSale->end_date); ?>"></div>
                            <i><?php echo BaseHelper::clean(__('Remains until the <br> end of the offer')); ?></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/shortcodes/deal-product/styles/wooden.blade.php ENDPATH**/ ?>