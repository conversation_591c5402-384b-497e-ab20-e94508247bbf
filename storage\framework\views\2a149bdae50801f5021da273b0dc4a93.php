<section class="product-area pt-30 pb-30">
    <div class="container">
        <div class="row">
            <div class="col-md-4 col-12">
                <div class="tpsection mb-40">
                    <h2 class="tpsection__title"><?php echo BaseHelper::clean($shortcode->title); ?></h2>
                </div>
            </div>
            <div class="col-md-8 col-12">
                <div class="tpnavbar mb-40">
                    <nav>
                        <div class="nav nav-tabs" id="product-type-tab" role="tablist" data-route="<?php echo e(route('public.ajax.products')); ?>" data-limit="<?php echo e($shortcode->limit); ?>">
                            <?php $__currentLoopData = $tabs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if(! in_array($key, $selectedTabs) || (! EcommerceHelper::isReviewEnabled() && $key === 'top-rated')) continue; ?>

                                <button class="<?php echo \Illuminate\Support\Arr::toCssClasses(['nav-link', 'active' => $loop->first]); ?>" data-type="<?php echo e($key); ?>" id="nav-<?php echo e($key); ?>-tab" data-bs-toggle="tab" data-bs-target="#nav-<?php echo e($key); ?>" type="button" role="tab" aria-controls="nav-<?php echo e($key); ?>-tab" <?php if($loop->first): ?> aria-selected="true" <?php endif; ?>>
                                    <?php echo e($value); ?>

                                </button>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </nav>
                </div>
            </div>
        </div>
        <div class="tab-content position-relative" id="nav-tabContent">
            <div class="tab-pane fade show active" role="tabpanel" aria-labelledby="nav-all-tab"></div>
            <div class="loading-spinner"></div>
        </div>
    </div>
</section>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/shortcodes/products/index.blade.php ENDPATH**/ ?>