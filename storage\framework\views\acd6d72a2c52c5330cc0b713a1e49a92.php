<?php
    $itemsPerRow ??= get_products_per_row();

    $itemsPerRowOnMobile = theme_option('ecommerce_products_per_row_mobile', 2);
?>

<div class="row row-cols-xxl-<?php echo e($itemsPerRow); ?> row-cols-lg-<?php echo e($itemsPerRow - 1); ?> row-cols-md-<?php echo e($itemsPerRow - 2); ?> row-cols-sm-<?php echo e($itemsPerRowOnMobile); ?> row-cols-<?php echo e($itemsPerRowOnMobile); ?>">
    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="col">
            <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.product-item'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.quick-view-modal'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/views/ecommerce/includes/product-grid.blade.php ENDPATH**/ ?>