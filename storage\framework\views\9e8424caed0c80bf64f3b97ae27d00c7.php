<?php
    $groupedCategories = $categories->groupBy('parent_id');

    $currentCategories = $groupedCategories->get(0);
    $limit = theme_option('ecommerce_header_categories_limit', 10);
?>

<?php if($currentCategories): ?>
    <?php
        $hasMoreCategories = $currentCategories->count() > $limit + 1;
    ?>

    <div class="cat-menu__category p-relative">
        <?php if(theme_option('collapsing_product_categories_on_homepage', 'no') == 'yes' || theme_option('header_style', 'default') == 'collapsed'): ?>
            <a class="tp-cat-toggle js-tp-cat-toggle" href="#" title="Expand/Collapse">
                <i class="fal fa-bars"></i> <?php echo e(__('Categories')); ?>

            </a>
        <?php else: ?>
            <span class="tp-cat-toggle">
                <i class="fal fa-bars"></i> <?php echo e(__('Categories')); ?>

            </span>
        <?php endif; ?>
        <div class="category-menu category-menu-off" style="<?php echo \Illuminate\Support\Arr::toCssStyles(['display: none !important' => theme_option('collapsing_product_categories_on_homepage', 'no') == 'yes' || url()->current() != BaseHelper::getHomepageUrl()]) ?>">
            <ul class="cat-menu__list">
                <?php $__currentLoopData = $currentCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $hasChildren = $groupedCategories->has($category->id);
                    ?>
                    <li class="<?php echo \Illuminate\Support\Arr::toCssClasses(['menu-item-has-children' => $hasChildren, 'hidden-to-toggle' => $hasMoreCategories ? $loop->iteration > $limit : $loop->iteration > $limit + 1]); ?>">
                        <a href="<?php echo e(route('public.single', $category->url)); ?>">
                            <?php if($categoryImage = $category->icon_image): ?>
                                <img src="<?php echo e(RvMedia::getImageUrl($categoryImage)); ?>" alt="<?php echo e($category->name); ?>" width="18" height="18" class="me-1">
                            <?php elseif($categoryIcon = $category->icon): ?>
                                <i class="<?php echo e($categoryIcon); ?>"></i>
                            <?php endif; ?>
                            <?php echo e($category->name); ?>

                        </a>
                        <?php if($hasChildren && $currentCategories = $groupedCategories->get($category->id)): ?>
                            <ul class="submenu">
                                <?php $__currentLoopData = $currentCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $childCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><a href="<?php echo e(route('public.single', $childCategory->url )); ?>"><?php echo e($childCategory->name); ?></a></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        <?php endif; ?>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <?php if($hasMoreCategories): ?>
                <div class="more-categories">
                    <?php echo e(__('More Categories')); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/categories-dropdown.blade.php ENDPATH**/ ?>