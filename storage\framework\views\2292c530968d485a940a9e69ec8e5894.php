<section class="category-area pt-70 pb-70">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="tpsection mb-40">
                    <h2 class="tpsection__title"><?php echo BaseHelper::clean($shortcode->title); ?></h2>
                </div>
            </div>
        </div>
        <div class="custom-row category-border pb-45 justify-content-xl-between">
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="tpcategory mb-40">
                    <a href="<?php echo e($category->url); ?>">
                        <div class="tpcategory__icon p-relative">
                            <?php if($icon = $category->icon): ?>
                                <i class="<?php echo e($icon); ?>"></i>
                            <?php else: ?>
                                <img src="<?php echo e(RvMedia::getImageUrl($category->image, default: RvMedia::getDefaultImage())); ?>" alt="<?php echo e($category->name); ?>">
                            <?php endif; ?>
                            <span><?php echo e(number_format($category->products_count)); ?></span>
                        </div>
                    </a>
                    <div class="tpcategory__content">
                        <p class="tpcategory__title">
                            <a href="<?php echo e($category->url); ?>"><?php echo e($category->name); ?></a>
                        </p>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php /**PATH C:\xampp\htdocs\main\platform\themes/ninico/partials/shortcodes/product-categories/styles/wooden.blade.php ENDPATH**/ ?>