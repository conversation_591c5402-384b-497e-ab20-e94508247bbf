<link rel="stylesheet" href="<?php echo e(asset('vendor/core/plugins/sale-popup/css/sale-popup.css')); ?>?v=1.2.1">

<div class="sale-popup-section">
    <div
        class="sale-popup-container-wrap sales_animated hidden oh des_1 slpr_mb_ slpr_has_btns"
        data-stt='{
            "classDown":{
                "swing":"bounceOutDown",
                "shake":"bounceOutDown",
                "wobble":"bounceOutDown",
                "jello":"bounceOutDown",
                "slideInUp":"slideOutDown",
                "slideInLeft":"slideOutLeft",
                "fadeIn":"fadeOut",
                "fadeInLeft":"fadeOutLeft",
                "bounceInUp":"bounceOutDown",
                "bounceInLeft":"bounceOutLeft",
                "rotateInDownLeft":"rotateOutDownLeft",
                "rotateInUpLeft":"rotateOutDownLeft",
                "flipInX":"flipOutX",
                "zoomIn":"zoomOut",
                "rollIn":"rollOut"
            },
            "limit": <?php echo e($salePopupHelper->getSetting('limit_products', 20)); ?>,
            "pp_type": "2",
            "url": <?php echo json_encode($urls); ?>,
            "id": <?php echo json_encode($products->pluck('id')->all()); ?>,
            "image": <?php echo json_encode($images); ?>,
            "starTime": 5,
            "starTime_unit": 1000,
            "stayTime": 10,
            "stayTimeUnit": 1000,
            "classUp": "slideInUp"
        }'
    >
        <div class="sale-popup-container">
            <div class="sale-popup-thumb">
                <a
                    class="js-sale-popup-a"
                    href="/"
                >
                    <img
                        class="js-sale-popup-img"
                        src="/"
                        srcset="/"
                        alt="sales popup"
                        loading="lazy"
                    >
                </a>
            </div>
            <div class="sale-popup-info">
                <span class="sale-popup-location">
                    <span class="js-sale-popup-location"></span>
                    <?php echo e($salePopupHelper->getSetting('purchased_text', 'purchased')); ?>

                </span>
                <a
                    class="js-sale-popup-a sale-popup-title js-sale-popup-tt"
                    href="/"
                ></a>
                <div class="sale-popup-ago">
                    <?php if($salePopupHelper->getSetting('show_time_ago_suggest', 1)): ?>
                        <span class="sale-popup-time js-sale-popup-ago"></span>
                    <?php endif; ?>
                    <?php if($salePopupHelper->getSetting('show_verified', 1)): ?>
                        <span class="sale-popup-verify">
                            <?php if (isset($component)) { $__componentOriginal73995948b3bd877b76251b40caf28170 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal73995948b3bd877b76251b40caf28170 = $attributes; } ?>
<?php $component = Botble\Icon\View\Components\Icon::resolve(['name' => 'ti ti-circle-check'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Botble\Icon\View\Components\Icon::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wrapper' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $attributes = $__attributesOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__attributesOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $component = $__componentOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__componentOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
                            <?php echo e($salePopupHelper->getSetting('verified_text', 'Verified')); ?>

                        </span>
                    <?php endif; ?>
                </div>
            </div>
            <?php if($salePopupHelper->getSetting('show_close_button', 1)): ?>
                <a
                    class="sale-popup-close pa"
                    href="#"
                    rel="nofollow"
                    title="close"
                >
                    <?php if (isset($component)) { $__componentOriginal73995948b3bd877b76251b40caf28170 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal73995948b3bd877b76251b40caf28170 = $attributes; } ?>
<?php $component = Botble\Icon\View\Components\Icon::resolve(['name' => 'ti ti-x'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Botble\Icon\View\Components\Icon::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wrapper' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $attributes = $__attributesOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__attributesOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $component = $__componentOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__componentOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
                </a>
            <?php endif; ?>
            <?php if($salePopupHelper->getSetting('show_quick_view_button', 1)): ?>
                <a
                    class="js-sale-popup-a js-sale-popup-quick-view-button sale-popup-quick-view pa op__0"
                    data-url=""
                    data-base-url="<?php echo e(url('')); ?>"
                    href="#"
                    rel="nofollow"
                    title="<?php echo e($salePopupTitle = $salePopupHelper->getSetting('quick_view_text', __('Quick view'))); ?>"
                >
                    <span title="<?php echo e($salePopupTitle); ?>">
                        <?php if (isset($component)) { $__componentOriginal73995948b3bd877b76251b40caf28170 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal73995948b3bd877b76251b40caf28170 = $attributes; } ?>
<?php $component = Botble\Icon\View\Components\Icon::resolve(['name' => 'ti ti-eye'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Botble\Icon\View\Components\Icon::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wrapper' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $attributes = $__attributesOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__attributesOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal73995948b3bd877b76251b40caf28170)): ?>
<?php $component = $__componentOriginal73995948b3bd877b76251b40caf28170; ?>
<?php unset($__componentOriginal73995948b3bd877b76251b40caf28170); ?>
<?php endif; ?>
                    </span>
                </a>
            <?php endif; ?>
        </div>
    </div>
    <script type="application/json" id="title-sale-popup">
        <?php echo json_encode($products->pluck('name')->all()); ?>

    </script>
    <script type="application/json" id="location-sale-popup">
        <?php echo json_encode(array_map('trim', explode('|', $salePopupHelper->getSetting('list_users_purchased', 'Nathan (California) | Alex (Texas) | Henry (New York) | Kiti (Ohio) | Daniel (Washington) | Hau (California) | Van (Ohio) | Sara (Montana)  | Kate (Georgia)')))); ?>

    </script>
    <script type="application/json" id="time-sale-popup">
        <?php echo json_encode(array_map('trim', explode('|', $salePopupHelper->getSetting('list_sale_time', '4 hours ago | 2 hours ago | 45 minutes ago | 1 day ago | 8 hours ago | 10 hours ago | 25 minutes ago | 2 day ago | 5 hours ago | 40 minutes ago')))); ?>

    </script>
</div>
<?php /**PATH C:\xampp\htdocs\main\platform/plugins/sale-popup/resources/views/sale-popup.blade.php ENDPATH**/ ?>