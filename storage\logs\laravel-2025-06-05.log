[2025-06-05 13:40:38] production.ERROR: Call to undefined function Botble\SalePopup\Http\Controllers\get_products() {"exception":"[object] (Error(code: 0): Call to undefined function Botble\\SalePopup\\Http\\Controllers\\get_products() at C:\\Users\\<USER>\\Desktop\\main\\platform\\plugins\\sale-popup\\src\\Http\\Controllers\\SalePopupController.php:18)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\SalePopup\\Http\\Controllers\\SalePopupController->ajaxSalePopup()
#1 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#2 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#3 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#4 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#6 C:\\Users\\<USER>\\Desktop\\main\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\RequiresJsonRequestMiddleware.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#7 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\RequiresJsonRequestMiddleware->handle()
#8 C:\\Users\\<USER>\\Desktop\\main\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\main\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\EnsureLicenseHasBeenActivated.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle()
#12 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\Users\\<USER>\\Desktop\\main\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then()
#14 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle()
#15 C:\\Users\\<USER>\\Desktop\\main\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle()
#17 C:\\Users\\<USER>\\Desktop\\main\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle()
#19 C:\\Users\\<USER>\\Desktop\\main\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle()
#21 C:\\Users\\<USER>\\Desktop\\main\\vendor\\botble\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle()
#23 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#25 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#27 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#29 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#31 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#32 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#34 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#36 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#38 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#39 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#40 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#41 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#42 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#43 C:\\Users\\<USER>\\Desktop\\main\\vendor\\botble\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\JsValidation\\RemoteValidationMiddleware->handle()
#45 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#53 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#61 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#63 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#65 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 C:\\Users\\<USER>\\Desktop\\main\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#68 {main}
"} 
[2025-06-05 13:48:28] production.ERROR: Target class [Botble\Language\Database\Seeders\LanguageSeeder] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [Botble\\Language\\Database\\Seeders\\LanguageSeeder] does not exist. at C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#1 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#2 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#3 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#4 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(133): Illuminate\\Foundation\\Application->make()
#5 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(48): Illuminate\\Database\\Seeder->resolve()
#6 C:\\Users\\<USER>\\Desktop\\main\\database\\seeders\\DatabaseSeeder.php(21): Illuminate\\Database\\Seeder->call()
#7 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#8 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#10 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#11 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#12 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#13 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#14 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#15 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#16 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#17 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#18 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#20 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#21 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#22 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#23 C:\\Users\\<USER>\\Desktop\\main\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#24 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#25 C:\\Users\\<USER>\\Desktop\\main\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#26 C:\\Users\\<USER>\\Desktop\\main\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#27 C:\\Users\\<USER>\\Desktop\\main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#28 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#29 C:\\Users\\<USER>\\Desktop\\main\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle()
#30 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"Botble\\Language\\Database\\Seeders\\LanguageSeeder\" does not exist at C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct()
#1 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build()
#2 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve()
#3 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve()
#4 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make()
#5 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(133): Illuminate\\Foundation\\Application->make()
#6 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(48): Illuminate\\Database\\Seeder->resolve()
#7 C:\\Users\\<USER>\\Desktop\\main\\database\\seeders\\DatabaseSeeder.php(21): Illuminate\\Database\\Seeder->call()
#8 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#9 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#11 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#12 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#13 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#14 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#15 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#16 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#17 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#18 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#19 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#21 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#22 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#23 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#24 C:\\Users\\<USER>\\Desktop\\main\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#25 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#26 C:\\Users\\<USER>\\Desktop\\main\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#27 C:\\Users\\<USER>\\Desktop\\main\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#28 C:\\Users\\<USER>\\Desktop\\main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#29 C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#30 C:\\Users\\<USER>\\Desktop\\main\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle()
#31 {main}
"} 
[2025-06-05 14:00:31] production.ERROR: Maximum execution time of 300 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 300 seconds exceeded at C:\\Users\\<USER>\\Desktop\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ServeCommand.php:131)
[stacktrace]
#0 {main}
"} 
[2025-06-05 18:36:18] production.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'performance_schema.session_status' doesn't exist (Connection: mysql, SQL: select variable_value as `Value` from performance_schema.session_status where variable_name = 'threads_connected') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'performance_schema.session_status' doesn't exist (Connection: mysql, SQL: select variable_value as `Value` from performance_schema.session_status where variable_name = 'threads_connected') at C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select()
#3 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne()
#4 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(633): Illuminate\\Database\\Connection->scalar()
#5 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(52): Illuminate\\Database\\Connection->threadCount()
#6 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\ShowCommand->handle()
#7 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#9 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#10 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#11 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#12 C:\\xampp\\htdocs\\main\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#13 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#14 C:\\xampp\\htdocs\\main\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#15 C:\\xampp\\htdocs\\main\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#16 C:\\xampp\\htdocs\\main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#17 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#18 C:\\xampp\\htdocs\\main\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle()
#19 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'performance_schema.session_status' doesn't exist at C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare()
#1 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select()
#5 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne()
#6 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(633): Illuminate\\Database\\Connection->scalar()
#7 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(52): Illuminate\\Database\\Connection->threadCount()
#8 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\ShowCommand->handle()
#9 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#11 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#12 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#13 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#14 C:\\xampp\\htdocs\\main\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute()
#15 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#16 C:\\xampp\\htdocs\\main\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run()
#17 C:\\xampp\\htdocs\\main\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()
#18 C:\\xampp\\htdocs\\main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun()
#19 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#20 C:\\xampp\\htdocs\\main\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle()
#21 {main}
"} 
[2025-06-05 18:41:48] production.ERROR: Maximum execution time of 300 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 300 seconds exceeded at C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ServeCommand.php:131)
[stacktrace]
#0 {main}
"} 
[2025-06-05 18:43:40] production.ERROR: Class "Barryvdh\Debugbar\ServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Barryvdh\\Debugbar\\ServiceProvider\" not found at C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider()
#1 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest()
#2 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load()
#3 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap()
#5 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#6 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 C:\\xampp\\htdocs\\main\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle()
#8 {main}
"} 
[2025-06-05 18:43:56] production.ERROR: Class "Barryvdh\Debugbar\ServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Barryvdh\\Debugbar\\ServiceProvider\" not found at C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider()
#1 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest()
#2 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load()
#3 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap()
#5 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#6 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 C:\\xampp\\htdocs\\main\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle()
#8 {main}
"} 
[2025-06-05 18:44:36] production.ERROR: Class "Barryvdh\Debugbar\ServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Barryvdh\\Debugbar\\ServiceProvider\" not found at C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider()
#1 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest()
#2 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load()
#3 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap()
#5 C:\\xampp\\htdocs\\main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#6 C:\\xampp\\htdocs\\main\\reset_admin_password.php(6): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 {main}
"} 
